import { Select } from "@streamax/poppy";
import type { IBaseSelectUIProps } from "../interface";
import {IconReset} from "@streamax/poppy-icons";
import './index.less';
const BaseSelectUI =  (props: IBaseSelectUIProps)=>{
    const { showReload, load, ...selectProps } = props;


    return <>
        <Select
            className={'base-select-ui'}
            {...(showReload ? {suffixIcon: <IconReset onClick={load} />} : {})}
            {...selectProps}
        />
    </>;

};

export default BaseSelectUI;
