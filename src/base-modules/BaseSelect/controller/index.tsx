import type {IBaseSelectProps, IOption} from "@/base-modules/BaseSelect/interface";
import {useAsyncEffect} from "@streamax/hooks";
import { useState } from "react";
import BaseSelectUI from "../ui";


const BaseSelect = (props: IBaseSelectProps) => {
    const { loadDataFn, ...selectProps } = props;
    const [disabled, setDisabled] = useState(true);
    const [showReload, setShowReload] = useState(false);
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<IOption[]>([]);

    useAsyncEffect(async ()=>{
        await load();
    }, []);

    // 加载数据
    const load = async ()=> {
        try {
            setShowReload(false);
            setLoading(true);
            setDisabled(true);
            const options =  await loadDataFn();
            setOptions(options);
            setLoading(false);
            setDisabled(false);
        } catch (error){
            console.error('Failed to load options:', error);
            setLoading(false);
            setShowReload(true);
            setDisabled(true);
        }
    };

    return <BaseSelectUI
        disabled={disabled}
        loading={loading}
        showReload={showReload}
        load={load}
        options={options}
        {...selectProps}
    />;
};

export default BaseSelect;
