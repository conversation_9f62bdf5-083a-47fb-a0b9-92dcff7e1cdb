import { SelectProps } from "@streamax/poppy/lib/select";

export interface IBaseSelectUIProps extends SelectProps {
    /** 是否禁用下拉框 */
    disabled: boolean;
    /** 是否处于数据加载状态 */
    loading: boolean;
    /** 下拉值改变 */
    onChange?: (value: any, option: any) => void;
    /** 展示重新加载按钮 */
    showReload: boolean;
    /** 加载下拉数据 */
    load: () => Promise<void>;
}

export interface IOption {
    value: string | number;
    label: string;
    [key: string]: any;
}

export interface IBaseSelectProps extends SelectProps{
    /** 下拉值改变 */
    onChange?: (value: any, option: any) => void;
    /** 加载下拉数据 */
    loadDataFn: () => Promise<IOption[]>;
}
