import {useEffect, useRef, useState} from "react";
import DeviceModelSelectUI from "../ui";
import type { IDeviceModelSelectProps } from "../interface";
import DeviceModelSelectData from "../data";

const dataInstance = new DeviceModelSelectData();

const DeviceModelSelect = (props: IDeviceModelSelectProps) => {
    const { getDeviceModelOptionsFn, disabledKeys, ...selectProps }
        = props;

    useEffect(() => {
        if (disabledKeys) {
            dataInstance.setDisableKeys(disabledKeys);
        }
    }, [disabledKeys]);


    return <DeviceModelSelectUI
        loadDataFn={
            getDeviceModelOptionsFn || dataInstance.loadDeviceModelOptionsFn
        }
        options={dataInstance.getDeviceModelOptions()}
        {...selectProps}
    />;
};

export { DeviceModelSelect, dataInstance };
