import type { IDeviceModel } from "../interface";

export default class DeviceModelSelectData {
    // 下拉列表
    private deviceModelOptions: IDeviceModel[];
    // 已选设备机型，不展示
    private disableKeys: string[];
    // 当前选择的机型key
    private selectKey: string | undefined;

    constructor(){
        this.deviceModelOptions = [];
        this.disableKeys = [];
    }

    getDeviceModelOptions(){
        return this.deviceModelOptions;
    }

    async loadDeviceModelOptionsFn(){
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.deviceModelOptions =  [{
            value: '1',
            label: '1'
        }];
        return this.deviceModelOptions;
    }

    clear(){
        this.selectKey = undefined;
    }

    getValue(){
        return {
            value: this.selectKey,
            option: this.deviceModelOptions.find(
                (item: IDeviceModel) => item.value === this.selectKey
            )
        };
    }

    setDisableKeys(keys: string[]){
        this.disableKeys = keys;
        // 再根据keys对this.deviceModelOptions进行过滤
        this.deviceModelOptions = this.deviceModelOptions.filter(
            (item: IDeviceModel) => !this.disableKeys.includes(item.value)
        );
    }

    setSelectKey(key: string){
        //需要检测key是否在this.disableKeys中，如在，则为undefined
        if (this.disableKeys.includes(key)) {
            this.selectKey = undefined;
            return;
        }
        this.selectKey = key;
    }
};
