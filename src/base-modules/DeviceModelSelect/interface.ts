import type {IBaseSelectProps} from "@/base-modules/BaseSelect/interface";

export interface IDeviceModelSelectUIProps extends IBaseSelectProps {

}

export interface IDeviceModelSelectProps {
    /** 已选择的车组ID列表，用于禁用选项 */
    disabledKeys?: string[];
    /** 外部自定义获取车组下拉选项逻辑 */
    getDeviceModelOptionsFn?: () => Promise<IDeviceModel[]>;
    /** 值变化回调 */
    onChange?: (value: string | number, option: IDeviceModel) => void;
    /** 其他数据 */
    [key: string]: any
}

export interface IDeviceModel {
    label: string;
    value: string;
    // 其他数据
    [key: string]: any
}
