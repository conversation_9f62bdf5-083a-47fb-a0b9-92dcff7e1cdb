import type {
    Map,
} from 'leaflet';
export type Origin = 'device' | 'platform';
export type ShapeInstance = any;

export interface IOptions {
    map?: Map;
    L: any;
    style?: any;
    onInstanceCreate?: () => void;
    onInstanceDestory?: () => void;
    onDrawEnd?: (
        instance: ShapeInstance | InstanceList,
        selectedInstance?: ShapeInstance[],
    ) => void;
}

export interface StreamMap extends Map {
    plot: {
        draw: (type: string, options: any, callback: (pg: any) => void) => void;
        enable: () => void;
        disable: () => void;
    };
}
export interface InstanceList {
    id: string;
    instance: ShapeInstance;
}
export interface AreaPoint {
    lat: number;
    lng: number;
    order: number;
}

// 多边形的gps数据
export type GPSArr = AreaPoint[];
// 圆的gps数据
export type CircleRadius = number;
export interface ShapeItem {
    // 圆形的参数不是单纯的gpsArr
    gps: (AreaPoint | GPSArr | CircleRadius)[];
    // 图形标志位id
    id: string;
    // 图形类型：圆、多边形、线
    type: number;
    // 是否选中
    isSelected: boolean;
    // 线段样式
    style: {
        color: string;
        fillColor: string;
        weight: number;
        dashArray: number[];
    };
}
